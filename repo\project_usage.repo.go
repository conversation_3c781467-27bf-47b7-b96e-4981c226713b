package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ProjectUsageOption func(repository.IRepository[models.ProjectUsage])

var ProjectUsage = func(c core.IContext, options ...ProjectUsageOption) repository.IRepository[models.ProjectUsage] {
	r := repository.New[models.ProjectUsage](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func ProjectUsageOrderBy(pageOptions *core.PageOptions) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func ProjectUsageWithProject() ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Preload("Project")
	}
}

func ProjectUsageWithCycle() ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Preload("Cycle")
	}
}

func ProjectUsageWithAllRelations() ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Preload("Project")
		c.Preload("Cycle")
	}
}

func ProjectUsageByProject(projectID string) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Where("project_id = ?", projectID)
	}
}

func ProjectUsageByCycle(cycleID string) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		c.Where("cycle_id = ?", cycleID)
	}
}

func ProjectUsageByAmountRange(minAmount, maxAmount *float64) ProjectUsageOption {
	return func(c repository.IRepository[models.ProjectUsage]) {
		if minAmount != nil && maxAmount != nil {
			c.Where("amount >= ? AND amount <= ?", *minAmount, *maxAmount)
		} else if minAmount != nil {
			c.Where("amount >= ?", *minAmount)
		} else if maxAmount != nil {
			c.Where("amount <= ?", *maxAmount)
		}
	}
}

func ProjectUsageBySearch(search string) ProjectUsageOption {
	if search == "" {
		return func(c repository.IRepository[models.ProjectUsage]) {}
	}
	return func(c repository.IRepository[models.ProjectUsage]) {
		// Search by ID or amount
		c.Where("id::text ILIKE ? OR amount::text ILIKE ?", "%"+search+"%", "%"+search+"%")
	}
}
